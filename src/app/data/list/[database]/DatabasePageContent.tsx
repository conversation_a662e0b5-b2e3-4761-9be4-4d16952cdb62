"use client";

import { useState, useEffect, useCallback, useRef, useLayoutEffect, useMemo } from "react";
import { debounce } from 'lodash';
import { useSearchParams } from "next/navigation";
import { searchData, advancedSearch, type DataRecord } from "@/lib/api";
import { Button } from "@/components/ui/button";
import AccessRestrictedAlert from "@/components/AccessRestrictedAlert";
import { useAuth } from "@/lib/auth";
import { useResizableColumns } from "@/hooks/useResizableColumns";
import ColumnResizer from "@/components/ColumnResizer";

import { trackEvent, trackSearch, trackPageView, setUserId } from '@/lib/enhanced-analytics';
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  ChevronLeft,
  ChevronRight,
  Filter,
  Download,
  ArrowUpDown,
  ChevronUp,
  ChevronDown,
  X,
  Loader2,
  BarChart3,
  AlertCircle,
  Database,
  RotateCcw
} from "lucide-react";
import Link from "next/link";



import { Sheet, SheetContent, SheetHeader, SheetTitle, SheetTrigger } from "@/components/ui/sheet";
import { DateRangePicker } from "@/components/ui/date-range-picker";
import type { DatabaseConfig, DatabaseFieldConfig } from "@/lib/configCache";
import CollapsibleStatsPanel from "@/components/CollapsibleStatsPanel";
import AdvancedSearch, { type SearchCondition } from "@/components/AdvancedSearch";
import { MultiSelect } from "@/components/ui/multi-select";
import SearchChips from "@/components/SearchChips";
import { useSearchChips } from "@/hooks/useSearchChips";

import { smartFormatDate } from "@/lib/utils";

// 临时组件定义
const FilterSkeleton = () => (
  <div className="space-y-2">
    <div className="h-4 bg-gray-200 rounded animate-pulse"></div>
    <div className="h-8 bg-gray-200 rounded animate-pulse"></div>
  </div>
);



interface DatabasePageContentProps {
  database: string;
}

// 数据库名称映射（将在组件内动态获取）



export default function DatabasePageContent({ database }: DatabasePageContentProps) {
  const searchParams = useSearchParams();
  const { user } = useAuth();

  // Refs for scroll synchronization and layout
  const headerScrollRef = useRef<HTMLDivElement>(null);
  const bodyScrollRef = useRef<HTMLDivElement>(null);
  const databaseTitleRef = useRef<HTMLDivElement>(null);
  const headerFixedRef = useRef<HTMLDivElement>(null);

  // 权限检查状态 - 简化版本，因为SimpleAccessCheck已经处理了权限
  const [hasAccess, _setHasAccess] = useState<boolean>(true);
  const [requiredLevel, setRequiredLevel] = useState<string>('free');
  const [databaseConfig, setDatabaseConfig] = useState<DatabaseConfig | null>(null);
  const [permissionCheckComplete, _setPermissionCheckComplete] = useState(true); // 默认完成

  // 简化的数据库配置获取（不做权限检查，因为SimpleAccessCheck已经处理了）
  useEffect(() => {
    const fetchDatabaseConfig = async () => {
      try {
        const response = await fetch('/api/config/databases');
        if (response.ok) {
          const result = await response.json();
          if (result.success && result.data[database]) {
            const config = result.data[database];
            setDatabaseConfig(config);
            setRequiredLevel(config.accessLevel || 'free');
            console.log(`[DatabasePageContent] 数据库配置加载完成: ${database}`);
          }
        }
      } catch (error) {
        console.error('获取数据库配置失败:', error);
      }
    };

    fetchDatabaseConfig();
  }, [database]);

  // 初始化Analytics
  useEffect(() => {
    // 设置用户ID
    if (user?.id) {
      setUserId(user.id);
    }

    // 追踪页面浏览
    trackPageView({
      database,
      page_type: 'database_list',
      access_level: requiredLevel,
      has_access: hasAccess,
    });
  }, [database, user, requiredLevel, hasAccess]);

  // UI 状态
  const [filterOpen, setFilterOpen] = useState(true);
  const [mobileFilterOpen, setMobileFilterOpen] = useState(false);
  const [showStats, setShowStats] = useState(false);

  // 数据状态
  const [data, setData] = useState<DataRecord[]>([]);
  const [tableHeaders, setTableHeaders] = useState<DatabaseFieldConfig[]>([]);

  // 简化的布局状态
  const [headerTop, setHeaderTop] = useState(140);
  const [searchChipsTop, setSearchChipsTop] = useState(140);
  const [tableMarginTop, setTableMarginTop] = useState(0);


  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // 配置状态
  const [config, setConfig] = useState<DatabaseConfig | null>(null);
  const [configLoading, setConfigLoading] = useState(true);

  // 筛选和分页状态 - 分离待提交和已应用的筛选条件
  const [pendingFilters, setPendingFilters] = useState<Record<string, unknown>>({});
  const [appliedFilters, setAppliedFilters] = useState<Record<string, unknown>>({});
  const [filtersInitialized, setFiltersInitialized] = useState(false);
  const [advancedSearchConditions, setAdvancedSearchConditions] = useState<SearchCondition[]>([]);
  const [_isAdvancedSearchActive, setIsAdvancedSearchActive] = useState(false);
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 20,
    totalCount: 0,
    totalPages: 0,
    hasNext: false,
    hasPrev: false,
    maxPages: 100,  // 全局统一100页限制
    isAtMaxPages: false,
    maxPageSize: 100,
    defaultPageSize: 20,
  });

  // 跳转到页功能的状态
  const [jumpToPage, setJumpToPage] = useState('');

  // 排序状态 - 初始值将在配置加载后设置
  const [sortBy, setSortBy] = useState<string>('');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');
  // 新增：跟踪排序是否是用户主动选择的（而非系统默认）
  const [isUserSelectedSort, setIsUserSelectedSort] = useState<boolean>(false);

  // 滚动位置保持 - 使用 useRef 避免重渲染循环
  const scrollPositionRef = useRef<number>(0);
  const shouldRestoreScrollRef = useRef<boolean>(false);
  const isRestoringScrollRef = useRef<boolean>(false);
  const isDataLoadingRef = useRef<boolean>(false); // 新增：标记数据加载状态

  // Note: hasActiveFilters calculation moved to useSearchChips hook

  // 优化的布局计算
  useLayoutEffect(() => {
    const updateLayout = () => {
      let currentTop = 64; // 导航栏高度

      // 数据库标题区域
      if (databaseTitleRef.current) {
        currentTop = databaseTitleRef.current.offsetTop + databaseTitleRef.current.offsetHeight;
      }

      // 搜索标签位置（在表头之前）
      setSearchChipsTop(currentTop);

      // 搜索标签区域（如果有的话）
      if (searchChips.hasActiveFilters) {
        currentTop += 45; // 搜索标签紧凑模式约45px高度
      }

      setHeaderTop(currentTop);

      // 表头区域
      if (headerFixedRef.current) {
        const headerBottom = headerFixedRef.current.offsetTop + headerFixedRef.current.offsetHeight;
        setTableMarginTop(headerBottom);
      }
    };

    // 立即执行一次
    updateLayout();

    // 当showStats状态变化时，使用更精确的延迟时间
    if (showStats) {
      // 展开时等待动画完成
      const timer = setTimeout(updateLayout, 400); // 增加延迟时间确保动画完全完成
      return () => clearTimeout(timer);
    } else {
      // 收起时快速更新，但也要等待动画开始
      const timer = setTimeout(updateLayout, 100);
      return () => clearTimeout(timer);
    }
  }, [showStats, filterOpen, tableHeaders.length, searchChips.hasActiveFilters]);

  // 搜索标签管理 - 必须在所有条件返回之前调用
  const searchChips = useSearchChips({
    filters: appliedFilters,
    advancedConditions: advancedSearchConditions,
    sortBy: isUserSelectedSort ? sortBy : '', // 只有用户选择的排序才显示
    sortOrder,
    fieldConfigs: config?.fields?.map(f => ({
      fieldName: f.fieldName,
      displayName: f.displayName
    })) || [],
    onFilterChange: (key: string, value: unknown) => {
      // 更新待提交和已应用的筛选条件
      const newFilters = { ...appliedFilters };
      if (value === undefined || value === null || value === '') {
        delete newFilters[key];
      } else {
        newFilters[key] = value;
      }
      setPendingFilters(newFilters);
      setAppliedFilters(newFilters);
      // 立即触发搜索
      loadData(1, newFilters, sortBy, sortOrder);
    },
    onAdvancedConditionRemove: (conditionId: string) => {
      const newConditions = advancedSearchConditions.filter(c => c.id !== conditionId);
      setAdvancedSearchConditions(newConditions);
      // 立即触发搜索
      handleUnifiedSearch(newConditions, appliedFilters);
    },
    onSortChange: (newSortBy?: string, newSortOrder?: 'asc' | 'desc') => {
      const finalSortBy = newSortBy || '';
      const finalSortOrder = newSortOrder || 'desc';
      setSortBy(finalSortBy);
      setSortOrder(finalSortOrder);
      // 如果清除排序，则重置为非用户选择状态
      if (!newSortBy) {
        setIsUserSelectedSort(false);
      } else {
        // 如果设置排序，则标记为用户选择
        setIsUserSelectedSort(true);
      }
      // 立即触发搜索
      loadData(pagination.page, appliedFilters, finalSortBy, finalSortOrder);
    },
    onClearAll: () => {
      // 清除所有搜索条件
      setPendingFilters({});
      setAppliedFilters({});
      setAdvancedSearchConditions([]);
      setSortBy('');
      setSortOrder('desc');
      // 重置为非用户选择状态
      setIsUserSelectedSort(false);
      // 立即触发搜索
      loadData(1, {}, '', 'desc');
    }
  });

  // 初始化可调整列宽功能 - 使用 useMemo 避免每次渲染都创建新的 initialColumns 数组
  const initialColumns = useMemo(() =>
    tableHeaders.map(header => ({
      fieldName: header.fieldName,
      fieldType: header.fieldType,
      displayName: header.displayName
    })), [tableHeaders]);

  const resizableColumns = useResizableColumns({
    initialColumns
  });

  // 获取列宽的辅助函数 - 保留原有逻辑作为备用
  const _getColumnWidth = (fieldName: string, fieldType: string) => {
    if (['productName', 'companyName'].includes(fieldName)) {
      return 'w-60'; // 重要字段 240px
    } else if (['registrationNumber'].includes(fieldName)) {
      return 'w-48'; // 注册证编号 192px
    } else if (['structureOrUse', 'specifications', 'structure'].includes(fieldName)) {
      return 'w-64'; // 长文本字段 256px
    } else if (['category', 'managementType'].includes(fieldName)) {
      return 'w-36'; // 分类字段 144px
    } else if (fieldType === 'date') {
      return 'w-32'; // 日期字段 128px
    }
    return 'w-40'; // 默认宽度 160px
  };

  // 检查是否为第一列（需要固定）
  const isFirstColumn = (index: number) => index === 0;

  // 元数据状态
  const [metadata, setMetadata] = useState<Record<string, string[]>>({});
  const [metadataWithCounts, setMetadataWithCounts] = useState<Record<string, Array<{ value: string; count: number }>>>({});
  const [dynamicCounts, setDynamicCounts] = useState<Record<string, Array<{ value: string; count: number }>>>({});
  const [metaLoading, setMetaLoading] = useState(true);

  const [statsPanelMaxHeight, setStatsPanelMaxHeight] = useState(400);
  const statsPanelRef = useRef<HTMLDivElement>(null);
  const footerRef = useRef<HTMLDivElement>(null);



  const databaseName = (databaseConfig as any)?.name || database;

  // 从URL search params 初始化筛选条件
  useEffect(() => {
    const newFilters: Record<string, unknown> = {};
    searchParams.forEach((value, key) => {
      newFilters[key] = value;
    });

    console.log('[DatabasePageContent] URL参数解析:', newFilters);

    // 初始化时，URL参数既是待提交的也是已应用的筛选条件
    setPendingFilters(prev => ({ ...prev, ...newFilters }));
    setAppliedFilters(prev => ({ ...prev, ...newFilters }));
    setFiltersInitialized(true); // 标记筛选条件已初始化
  }, [searchParams]);

  // 设置默认排序（当配置加载后）
  useEffect(() => {
    if (databaseConfig && !sortBy) {
      if (databaseConfig.defaultSort && databaseConfig.defaultSort.length > 0) {
        // 使用配置中的第一个排序字段作为默认排序
        const firstSort = databaseConfig.defaultSort[0];
        setSortBy(firstSort.field);
        setSortOrder(firstSort.order);
        // 默认排序不标记为用户选择
        setIsUserSelectedSort(false);
      } else {
        // 回退到id字段
        setSortBy('id');
        setSortOrder('desc');
        // 默认排序不标记为用户选择
        setIsUserSelectedSort(false);
      }
    }
  }, [databaseConfig, sortBy]);

  // 获取筛选元数据
  useEffect(() => {
    const fetchMetadata = async () => {
      try {
        setMetaLoading(true);
        const res = await fetch(`/api/meta/${database}`);
        const result = await res.json();
        if (result.success) {
          setMetadata(result.data);
          setMetadataWithCounts(result.dataWithCounts || {});
          // 不要用静态计数初始化动态计数，让动态计数保持空对象，等待API获取
          // 同时获取配置信息
          if (result.config) {
            setConfig(result.config);
            setConfigLoading(false);
          }
        }
      } catch (_e) {
        console.error("Failed to fetch metadata", _e);
      } finally {
        setMetaLoading(false);
      }
    };
    fetchMetadata();
  }, [database]);

  // 获取动态计数 - 使用已应用的筛选条件
  const fetchDynamicCounts = useCallback(async (fieldName: string) => {
    try {
      // 使用 ref 获取最新的 appliedFilters，避免依赖问题
      const currentFilters = { ...appliedFiltersRef.current };
      // 排除当前字段的筛选条件，但保留文本搜索条件
      delete currentFilters[fieldName];

      const params = new URLSearchParams({
        field: fieldName,
        filters: JSON.stringify(currentFilters)
      });

      const response = await fetch(`/api/meta/${database}/dynamic-counts?${params}`);
      const result = await response.json();

      if (result.success) {
        setDynamicCounts(prev => ({
          ...prev,
          [fieldName]: result.data
        }));
      }
    } catch (__error) {
      console.error('获取动态计数失败:', error);
    }
  }, [database]); // 只依赖 database，避免循环依赖

  // 使用 ref 来获取最新的 appliedFilters 值，避免闭包问题
  const appliedFiltersRef = useRef(appliedFilters);
  appliedFiltersRef.current = appliedFilters;

  // 防抖版本的 fetchDynamicCounts - 减少频繁的 API 请求
  // 修复：使用 ref 获取最新 appliedFilters，避免防抖函数重新创建
  const debouncedFetchDynamicCounts = useMemo(
    () => debounce(async (fieldName: string) => {
      try {
        // 使用 ref 获取最新的 appliedFilters 值
        const currentFilters = { ...appliedFiltersRef.current };
        // 排除当前字段的筛选条件，但保留文本搜索条件
        delete currentFilters[fieldName];

        const params = new URLSearchParams({
          field: fieldName,
          filters: JSON.stringify(currentFilters)
        });

        const response = await fetch(`/api/meta/${database}/dynamic-counts?${params}`);
        const result = await response.json();

        if (result.success) {
          setDynamicCounts(prev => ({
            ...prev,
            [fieldName]: result.data
          }));
        }
      } catch (__error) {
        console.error('获取动态计数失败:', error);
      }
    }, 300), // 300ms 延迟
    [database, setDynamicCounts] // 只依赖稳定的值
  );

  // 数据加载函数
  const loadData = useCallback(async (
    page: number,
    currentFilters: Record<string, unknown>,
    currentSortBy?: string,
    currentSortOrder?: 'asc' | 'desc'
  ) => {
    // 记录搜索开始时间
    const _loadStartTime = performance.now();

    try {
      setLoading(true);
      setError(null);

      // 记录加载开始时间
      const loadStartTime = performance.now();

      // 检查是否有 allFields 搜索，如果有则尝试使用统一搜索
      const allFieldsQuery = currentFilters.allFields as string;
      console.log('[loadData] 检查 allFields:', { allFieldsQuery, currentFilters, hasAllFields: !!allFieldsQuery });

      if (allFieldsQuery && allFieldsQuery.trim()) {
        try {
          console.log('Attempting unified search for allFields:', allFieldsQuery, 'database:', database);

          // 构建筛选条件（排除allFields，因为它由ES处理）
          const { allFields: _allFields, ...otherFilters } = currentFilters;

          // 使用统一搜索API
          const unifiedResponse = await fetch(
            `/api/unified-database-search/${database}?q=${encodeURIComponent(allFieldsQuery)}&page=${page}&limit=${pagination.limit}&sortBy=${currentSortBy || sortBy}&sortOrder=${currentSortOrder || sortOrder}&filters=${encodeURIComponent(JSON.stringify(otherFilters))}`
          );

          if (unifiedResponse.ok) {
            const unifiedData = await unifiedResponse.json();

            if (unifiedData.success && unifiedData.data) {
              console.log('Unified search successful, results:', unifiedData.pagination?.total_results || unifiedData.data.length);

              // 数据已经是标准格式，直接使用
              setData(unifiedData.data);

              // 设置表格头部（使用现有配置）
              if (config && config.fields) {
                const visibleFields = config.fields
                  .filter(f => f.isVisible)
                  .sort((a, b) => a.listOrder - b.listOrder);
                setTableHeaders(visibleFields);
              }

              setPagination(prev => ({
                ...prev,
                page: unifiedData.pagination?.page || page,
                totalCount: unifiedData.pagination?.total_results || unifiedData.data.length,
                totalPages: unifiedData.pagination?.total_pages || 1,
                hasNext: (unifiedData.pagination?.page || page) < (unifiedData.pagination?.total_pages || 1),
                hasPrev: (unifiedData.pagination?.page || page) > 1,
                isAtMaxPages: (unifiedData.pagination?.total_pages || 1) >= prev.maxPages,
                maxPages: prev.maxPages,
                maxPageSize: prev.maxPageSize,
                defaultPageSize: prev.defaultPageSize,
              }));

              // 追踪统一搜索事件
              trackSearch({
                query: allFieldsQuery,
                database,
                search_type: 'unified',
                results_count: unifiedData.data.length,
                filters_applied: currentFilters,
              });

              // 详细搜索分析
              fetch('/api/analytics/search', {
                method: 'POST',
                headers: {
                  'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                  userId: user?.id,
                  sessionId: sessionStorage.getItem('analytics_session_id') || undefined,
                  database,
                  searchType: 'unified_search',
                  searchQuery: allFieldsQuery,
                  searchFields: {
                    allFields: allFieldsQuery,
                  },
                  filters: currentFilters,
                  sortBy: currentSortBy || sortBy,
                  sortOrder: currentSortOrder || sortOrder,
                  resultsCount: unifiedData.data.length,
                  searchTime: unifiedData.search_info?.search_time || (performance.now() - loadStartTime),
                  esTime: unifiedData.search_info?.es_took,
                  missingIdsCount: unifiedData.search_info?.missing_ids_count,
                  userAgent: navigator.userAgent,
                }),
              }).catch(error => {
                console.warn('Failed to record unified search analytics:', error);
              });

              setLoading(false);
              return; // 统一搜索成功，直接返回
            } else {
              console.warn('Unified search returned no data:', unifiedData);
            }
          } else {
            console.warn('Unified search response not ok:', unifiedResponse.status);
          }
        } catch (unifiedError) {
          console.warn('Unified search failed, falling back to standard search:', unifiedError);
        }
      }

      // 回退到标准搜索
      console.log('[loadData] Using standard search API with filters:', currentFilters);
      const response = await searchData(
        database,
        page,
        pagination.limit,
        currentFilters,
        currentSortBy || sortBy,
        currentSortOrder || sortOrder
      );
      console.log('[loadData] Standard search response:', { success: response.success, dataLength: response.data?.length });

      if (response.success && response.data) {
        setData(response.data);

        // 从配置中获取表格头部信息
        if (response.config && response.config.fields) {
          const visibleFields = response.config.fields
            .filter(f => f.isVisible)
            .sort((a, b) => a.listOrder - b.listOrder);
          setTableHeaders(visibleFields);
        }

        setPagination(prev => ({
          ...prev,
          page: response.pagination.page,
          totalCount: response.pagination.totalCount,
          totalPages: response.pagination.totalPages,
          hasNext: response.pagination.hasNext,
          hasPrev: response.pagination.hasPrev,
          isAtMaxPages: response.pagination.isAtMaxPages,
          maxPages: response.pagination.maxPages,
          maxPageSize: response.pagination.maxPageSize,
          defaultPageSize: response.pagination.defaultPageSize,
        }));

        // 追踪搜索事件
        const searchQuery = currentFilters.allFields ||
                           currentFilters.productName ||
                           currentFilters.companyName ||
                           Object.values(currentFilters).find(v => typeof v ==="string" && v.length > 0) ||
                           '';

        if (searchQuery || Object.keys(currentFilters).length > 0) {
          // 原有的Analytics追踪
          trackSearch({
            query: String(searchQuery),
            database,
            search_type: 'simple',
            results_count: response.data.length,
            filters_applied: currentFilters,
          });

          // 新的详细搜索分析（通过API调用）
          fetch('/api/analytics/search', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              userId: user?.id,
              sessionId: sessionStorage.getItem('analytics_session_id') || undefined,
              database,
              searchType: 'standard',
              searchQuery: String(searchQuery),
              searchFields: {
                allFields: currentFilters.allFields,
                productName: currentFilters.productName,
                companyName: currentFilters.companyName,
                registrationNumber: currentFilters.registrationNumber,
                // 添加其他搜索字段
              },
              filters: currentFilters,
              sortBy: currentSortBy || sortBy,
              sortOrder: currentSortOrder || sortOrder,
              resultsCount: response.data.length,
              searchTime: performance.now() - loadStartTime,
              userAgent: navigator.userAgent,
            }),
          }).catch(error => {
            console.warn('Failed to record search analytics:', error);
          });
        }
      } else {
        setError(response.error || '加载数据失败');
      }
    } catch (__err) {
      console.error('Failed to load data:', __err);
      setError('网络错误，请稍后重试');
    } finally {
      setLoading(false);
    }
  }, [database, pagination.limit, sortBy, sortOrder, config]);

  // 初始数据加载 - 当配置加载完成且筛选条件初始化完成时执行
  useEffect(() => {
    console.log('[DatabasePageContent] 数据加载条件检查:', {
      metaLoading,
      configLoading,
      filtersInitialized,
      appliedFilters,
      hasAllFields: !!appliedFilters.allFields
    });

    if (!metaLoading && !configLoading && filtersInitialized) {
      console.log('[DatabasePageContent] 开始加载数据:', appliedFilters);
      loadData(1, appliedFilters);
    }
  }, [metaLoading, configLoading, filtersInitialized, appliedFilters, loadData]); // 确保筛选条件初始化后再加载数据

  // 初始化动态计数 - 页面加载时获取所有筛选器的初始计数
  useEffect(() => {
    if (!metaLoading && !configLoading && config) {
      const filterableFields = config.fields.filter((f: DatabaseFieldConfig) =>
        f.isFilterable &&
        (f.filterType ==="multi_select" || f.filterType ==="select" || f.filterType === 'checkbox')
      );

      filterableFields.forEach((fieldConfig: DatabaseFieldConfig) => {
        debouncedFetchDynamicCounts(fieldConfig.fieldName);
      });
    }
  }, [metaLoading, configLoading, config]); // 只在配置加载完成时执行一次

  // 兜底：当配置加载完成且存在数据但表头尚未设置时，根据配置设置表头
  useEffect(() => {
    if (config && config.fields && data.length > 0 && tableHeaders.length === 0) {
      const visibleFields = config.fields
        .filter((f: DatabaseFieldConfig) => f.isVisible)
        .sort((a: DatabaseFieldConfig, b: DatabaseFieldConfig) => a.listOrder - b.listOrder);
      setTableHeaders(visibleFields);
    }
  }, [config, data.length, tableHeaders.length]);

  // 当已应用的筛选条件改变时，更新所有筛选器的动态计数
  // 使用 useRef 来跟踪上次的 appliedFilters，避免无限循环
  const prevAppliedFiltersRef = useRef<Record<string, unknown>>({});

  useEffect(() => {
    if (!metaLoading && !configLoading && config) {
      // 检查 appliedFilters 是否真的发生了变化
      const filtersChanged = JSON.stringify(appliedFilters) !== JSON.stringify(prevAppliedFiltersRef.current);

      if (filtersChanged) {
        if (process.env.NODE_ENV === 'development') {

        }
        prevAppliedFiltersRef.current = { ...appliedFilters };

        // 先清空动态计数，强制重新获取
        setDynamicCounts({});

        const filterableFields = config.fields.filter((f) =>
          f.isFilterable &&
          (f.filterType ==="multi_select" || f.filterType ==="select" || f.filterType === 'checkbox')
        );

        // 使用 setTimeout 确保状态清空后再获取新数据
        setTimeout(() => {
          filterableFields.forEach((fieldConfig) => {
            fetchDynamicCounts(fieldConfig.fieldName); // 使用直接调用而不是防抖
          });
        }, 100);
      }
    }
  }, [appliedFilters, metaLoading, configLoading, config, fetchDynamicCounts]); // 当已应用筛选条件变化时执行

  // 滚动同步效果 - 表头跟随数据表格滚动，同时保存滚动位置
  useEffect(() => {
    const headerEl = headerScrollRef.current;
    const bodyEl = bodyScrollRef.current;

    if (headerEl && bodyEl) {
      const handleBodyScroll = () => {
        const scrollLeft = bodyEl.scrollLeft;
        headerEl.scrollLeft = scrollLeft;

        // 只有在非恢复状态且非数据加载状态时才保存滚动位置
        if (!isRestoringScrollRef.current && !isDataLoadingRef.current) {
          scrollPositionRef.current = scrollLeft;
        }
      };

      bodyEl.addEventListener('scroll', handleBodyScroll);

      return () => {
        bodyEl.removeEventListener('scroll', handleBodyScroll);
      };
    }
  }, [data, loading]); // 保持依赖，确保DOM更新后重新绑定

  // 滚动位置恢复效果 - 在数据加载完成后恢复滚动位置
  useEffect(() => {
    if (!loading) {
      if (data.length > 0 && shouldRestoreScrollRef.current) {
        const headerEl = headerScrollRef.current;
        const bodyEl = bodyScrollRef.current;

        if (headerEl && bodyEl) {
          // 使用 requestAnimationFrame 确保 DOM 完全渲染
          requestAnimationFrame(() => {
            isRestoringScrollRef.current = true;

            const targetScrollLeft = scrollPositionRef.current;
            bodyEl.scrollLeft = targetScrollLeft;
            headerEl.scrollLeft = targetScrollLeft;

            // 恢复完成后重置标志
            setTimeout(() => {
              isRestoringScrollRef.current = false;
              shouldRestoreScrollRef.current = false;
              isDataLoadingRef.current = false;
            }, 100);
          });
        } else {
          // 即使找不到元素，也要重置标志
          isDataLoadingRef.current = false;
          shouldRestoreScrollRef.current = false;
        }
      } else {
        // 如果不需要恢复，也要重置数据加载标志
        if (isDataLoadingRef.current) {
          isDataLoadingRef.current = false;
        }
      }
    }
  }, [loading, data.length]);

  // 动态计算统计面板最大高度
  useEffect(() => {
    function calcMaxHeight() {
      if (!statsPanelRef.current || !footerRef.current) return;
      const statsPanelTop = statsPanelRef.current.getBoundingClientRect().top;
      const footerTop = footerRef.current.getBoundingClientRect().top;
      const maxH = Math.max(footerTop - statsPanelTop - 16, 200); // 至少200px
      setStatsPanelMaxHeight(maxH);
    }
    calcMaxHeight();
    window.addEventListener('resize', calcMaxHeight);
    return () => window.removeEventListener('resize', calcMaxHeight);
  }, [showStats, filterOpen]);

  // 事件处理函数 - 批量筛选模式：只更新待提交的筛选条件
  const handleFilterChange = (key: string, value: unknown) => {
    setPendingFilters(prev => ({ ...prev, [key]: value }));
  };

  const handleSearch = async () => {
    // 保存当前滚动位置
    const bodyEl = bodyScrollRef.current;
    if (bodyEl) {
      scrollPositionRef.current = bodyEl.scrollLeft;
      shouldRestoreScrollRef.current = true;
      isDataLoadingRef.current = true;
    }

    // 批量筛选：将待提交的筛选条件应用为已应用的筛选条件
    setAppliedFilters(pendingFilters);

    // 使用统一搜索，同时应用高级搜索条件和待提交的筛选器
    await handleUnifiedSearch(advancedSearchConditions, pendingFilters);
  };

  const resetFilters = () => {
    // 重置筛选器时，重置滚动位置到开始
    scrollPositionRef.current = 0;
    shouldRestoreScrollRef.current = true;

    // 批量筛选：同时清空待提交和已应用的筛选条件
    setPendingFilters({});
    setAppliedFilters({});
    setAdvancedSearchConditions([]);
    setIsAdvancedSearchActive(false);

    // 清空动态计数并重新获取初始计数
    setDynamicCounts({});

    // 重新获取所有字段的初始动态计数
    if (config) {
      const filterableFields = config.fields.filter((f) =>
        f.isFilterable &&
        (f.filterType ==="multi_select" || f.filterType ==="select" || f.filterType === 'checkbox')
      );

      setTimeout(() => {
        filterableFields.forEach((fieldConfig) => {
          debouncedFetchDynamicCounts(fieldConfig.fieldName);
        });
      }, 100);
    }

    loadData(1, {});
  };

  // 统一搜索处理函数 - 同时处理高级搜索条件和简单过滤器
  const handleUnifiedSearch = async (
    conditions: SearchCondition[],
    currentFilters: Record<string, unknown>
  ) => {
    try {
      setLoading(true);
      setError(null);

      // 记录搜索开始时间
      const searchStartTime = performance.now();

      // 判断是否有任何搜索条件
      const hasAdvancedConditions = conditions.length > 0;
      const hasFilters = Object.keys(currentFilters).some(key =>
        currentFilters[key] !== undefined &&
        currentFilters[key] !== null &&
        currentFilters[key] !=="" );

      // 设置搜索状态
      if (hasAdvancedConditions) {
        setIsAdvancedSearchActive(true);
        setAdvancedSearchConditions(conditions);
      } else {
        setIsAdvancedSearchActive(false);
      }

      let response;

      if (hasAdvancedConditions) {
        // 有高级搜索条件时，使用高级搜索API
        response = await advancedSearch({
          database,
          conditions,
          page: 1,
          limit: pagination.limit,
          sortBy,
          sortOrder,
        });
      } else {
        // 没有高级搜索条件时，使用loadData函数（支持allFields等简单搜索）
        await loadData(1, currentFilters);
        return; // loadData会处理所有状态更新，直接返回
      }

      if (response.success && response.data) {
        setData(response.data);

        // 从配置中获取表格头部信息
        if (response.config && response.config.fields) {
          const visibleFields = response.config.fields
            .filter((f) => f.isVisible)
            .sort((a, b) => a.listOrder - b.listOrder);
          setTableHeaders(visibleFields);
        }

        setPagination(prev => ({
          ...prev,
          page: response.pagination.page,
          totalCount: response.pagination.totalCount,
          totalPages: response.pagination.totalPages,
          hasNext: response.pagination.hasNext,
          hasPrev: response.pagination.hasPrev,
          isAtMaxPages: response.pagination.isAtMaxPages,
          maxPages: response.pagination.maxPages,
          maxPageSize: response.pagination.maxPageSize,
          defaultPageSize: response.pagination.defaultPageSize,
        }));

        // 追踪搜索事件
        const searchType = hasAdvancedConditions && hasFilters ? 'unified' :
                          hasAdvancedConditions ? 'advanced' : 'simple';

        trackSearch({
          query: hasAdvancedConditions ?
            JSON.stringify(conditions) :
            JSON.stringify(currentFilters),
          database,
          search_type: searchType,
          results_count: response.data.length,
        });

        // 详细的搜索分析（通过API调用）
        fetch('/api/analytics/search', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            userId: user?.id,
            sessionId: sessionStorage.getItem('analytics_session_id') || undefined,
            database,
            searchType,
            searchQuery: hasAdvancedConditions ?
              conditions.map(c => `${c.field}:${c.value}`).join(' AND ') :
              Object.entries(currentFilters)
                .filter(([_, value]) => value !== undefined && value !== null && value !== '')
                .map(([key, value]) => `${key}:${value}`)
                .join(' AND '),
            searchFields: {
              ...currentFilters,
              ...(hasAdvancedConditions ? conditions.reduce((acc, c) => {
                acc[c.field] = c.value;
                return acc;
              }, {} as Record<string, any>) : {})
            },
            filters: {
              simple: currentFilters,
              advanced: hasAdvancedConditions ? conditions.reduce((acc, c) => {
                acc[c.field] = { value: c.value };
                return acc;
              }, {} as Record<string, any>) : {}
            },
            resultsCount: response.data.length,
            searchTime: performance.now() - searchStartTime,
            userAgent: navigator.userAgent,
          }),
        }).catch(error => {
          console.warn('Failed to record unified search analytics:', error);
        });
      } else {
        setError(response.error || 'Search failed');
      }
    } catch (__err) {
      console.error('Unified search failed:', __err);
      setError('Network error, please try again later');
    } finally {
      setLoading(false);
    }
  };

  // 高级搜索处理函数 - 现在使用统一搜索，使用已应用的筛选条件
  const handleAdvancedSearch = async (conditions: SearchCondition[]) => {
    await handleUnifiedSearch(conditions, appliedFilters);
  };

  const handleAdvancedSearchClear = async () => {
    // 保存当前滚动位置
    const bodyEl = bodyScrollRef.current;
    if (bodyEl) {
      scrollPositionRef.current = bodyEl.scrollLeft;
      shouldRestoreScrollRef.current = true;
      isDataLoadingRef.current = true;
    }

    setAdvancedSearchConditions([]);
    setIsAdvancedSearchActive(false);
    // 清除高级搜索后，仍然保持左侧面板的过滤器
    await handleUnifiedSearch([], appliedFilters);
  };

  const handlePageChange = (direction: 'prev' | 'next') => {
    // 保存当前滚动位置
    const bodyEl = bodyScrollRef.current;
    if (bodyEl) {
      scrollPositionRef.current = bodyEl.scrollLeft;
      shouldRestoreScrollRef.current = true;
      isDataLoadingRef.current = true;
    }

    const newPage = direction ==="prev" ? pagination.page - 1 : pagination.page + 1;

    // 修复翻页限制检查：计算有效的最大页数
    const effectiveMaxPages = Math.min(pagination.totalPages, pagination.maxPages);

    // 检查翻页限制：页码必须在有效范围内
    if (newPage >= 1 && newPage <= effectiveMaxPages) {
      loadData(newPage, appliedFilters);
    }
  };

  // 跳转到指定页面
  const handleJumpToPage = () => {
    const targetPage = parseInt(jumpToPage);

    // 验证输入
    if (isNaN(targetPage) || targetPage < 1) {
      alert('Please enter a valid page number (minimum 1)');
      return;
    }

    // 计算有效的最大页数
    const safeTotalPages = pagination.totalPages || 1;
    const safeMaxPages = pagination.maxPages || 100;
    const effectiveMaxPages = Math.min(safeTotalPages, safeMaxPages);

    if (targetPage > effectiveMaxPages) {
      if (safeTotalPages > safeMaxPages) {
        alert(`Page limit is ${safeMaxPages}. Please enter a page number between 1 and ${safeMaxPages}.`);
      } else {
        alert(`Maximum page is ${safeTotalPages}. Please enter a page number between 1 and ${safeTotalPages}.`);
      }
      return;
    }

    // 保存当前滚动位置
    const bodyEl = bodyScrollRef.current;
    if (bodyEl) {
      scrollPositionRef.current = bodyEl.scrollLeft;
      shouldRestoreScrollRef.current = true;
      isDataLoadingRef.current = true;
    }

    // 跳转到目标页面
    loadData(targetPage, appliedFilters);
    setJumpToPage(''); // 清空输入框
  };

  // 处理跳转输入框的回车键
  const handleJumpInputKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleJumpToPage();
    }
  };

  const handleRowClick = (row: DataRecord) => {
    trackEvent({
      event: 'detail_view',
      data: {
        database,
        rowId: row.id,
        productName: row.productName,
      }
    });
  };

  // 处理列排序
  const handleSort = (fieldName: string) => {
    // 检查字段是否可排序
    const field = config?.fields.find(f => f.fieldName === fieldName);
    if (!field?.isSortable) return;

    // 保存当前滚动位置并标记需要恢复
    const bodyEl = bodyScrollRef.current;
    if (bodyEl) {
      const currentScrollLeft = bodyEl.scrollLeft;
      scrollPositionRef.current = currentScrollLeft;
      shouldRestoreScrollRef.current = true;
      isDataLoadingRef.current = true; // 标记开始数据加载
    }

    let newSortOrder: 'asc' | 'desc' = 'desc';

    // 如果点击的是当前排序字段，则切换排序方向
    if (sortBy === fieldName) {
      newSortOrder = sortOrder ==="desc" ? 'asc' : 'desc';
    }

    setSortBy(fieldName);
    setSortOrder(newSortOrder);
    // 标记为用户主动选择的排序
    setIsUserSelectedSort(true);

    // 重新加载数据
    loadData(1, appliedFilters, fieldName, newSortOrder);
  };

  const handleExport = async (format: 'csv' | 'excel') => {
    try {
      const params = new URLSearchParams({
        format,
        ...Object.fromEntries(
          Object.entries(appliedFilters).filter(([, value]) => value !== null && value !== undefined && value !== '')
        ),
      });

      const response = await fetch(`/api/export/${database}?${params}`);

      if (!response.ok) {
        throw new Error('导出失败');
      }

      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.style.display = 'none';
      a.href = url;
      a.download = `${databaseName}_${new Date().toISOString().split('T')[0]}.${format ==="excel" ? 'xlsx' : 'csv'}`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);

      trackEvent({
        event: 'data_export',
        data: {
          database,
          format,
          filters: appliedFilters,
          recordCount: pagination.totalCount,
        }
      });
    } catch (__error) {
      console.error('Export failed:', error);
      setError('导出失败，请稍后重试');
    }
  };

  // 渲染动态筛选器 - 基于 FieldConfig
  const renderDynamicFilters = () => {
    if (metaLoading || configLoading) {
      return <FilterSkeleton />;
    }

    if (!config || !config.fields || !Array.isArray(config.fields)) {
      return null;
    }

    // 只显示可筛选的字段，按 sortOrder 排序
    const filterableFields = config.fields
      .filter(field => field.isFilterable)
      .sort((a, b) => a.sortOrder - b.sortOrder);

    return filterableFields.map((fieldConfig) => {
      const fieldName = fieldConfig.fieldName;
      const options = metadata[fieldName] || [];
      const optionsWithCounts = metadataWithCounts[fieldName] || [];
      const dynamicCountsForField = dynamicCounts[fieldName] || [];
      const validOptions = options.filter(option => option && option.trim && option.trim() !== '');

      // 优先使用动态计数，如果没有则使用静态计数
      // 添加调试日志（仅在开发环境）
      if (process.env.NODE_ENV === 'development' && (fieldName ==="review_panel" || fieldName ==="medicalspecialty" || fieldName ==="gmpexemptflag" || fieldName === 'thirdpartyflag')) {
        console.log(`🔍 ${fieldName} 筛选器:`, {
          dynamicCountsLength: dynamicCountsForField?.length || 0,
          staticCountsLength: optionsWithCounts?.length || 0,
          usingDynamic: dynamicCountsForField?.length > 0,
          dynamicCounts: dynamicCountsForField?.slice(0, 3) || [], // 只显示前3个避免日志过长
          staticCounts: optionsWithCounts?.slice(0, 3) || [], // 只显示前3个避免日志过长
          appliedFilters: Object.keys(appliedFilters || {}).length > 0 ? appliedFilters : 'empty'
        });
      }
      
      const mergedOptions = dynamicCountsForField.length > 0 ?
        dynamicCountsForField :
        optionsWithCounts;





      // 对于 select 和 multi_select 类型，如果没有选项则不显示
      // 修复：select 和 multi_select 都应该检查 mergedOptions
      if (fieldConfig.filterType ==="select" || fieldConfig.filterType === 'multi_select') {
        const hasOptions = mergedOptions.length > 0;

        if (!hasOptions) {
          return null;
        }
      }

      return (
        <div key={fieldName} className="space-y-1">
          <Label className="text-xs font-medium text-gray-700">
            {fieldConfig.displayName}
          </Label>
          {fieldConfig.filterType ==="select" ? (
            <Select
              value={(pendingFilters[fieldName] as string) || '__all__'}
              onValueChange={(value) => {
                handleFilterChange(fieldName, value ==="__all__" ? '' : value);
                // 批量筛选模式：不立即更新动态计数，等用户点击Apply Filters时再更新
              }}
            >
              <SelectTrigger className="w-full h-8 text-xs">
                <SelectValue placeholder={`Select ${fieldConfig.displayName}`} />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="__all__">All</SelectItem>
                {mergedOptions.length > 0 ? (
                  mergedOptions.map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      {option.value} ({option.count})
                    </SelectItem>
                  ))
                ) : (
                  validOptions.map((option) => (
                    <SelectItem key={option} value={option}>
                      {option}
                    </SelectItem>
                  ))
                )}
              </SelectContent>
            </Select>
          ) : fieldConfig.filterType ==="multi_select" ? (
            <MultiSelect
              options={mergedOptions.map(option => ({
                value: option.value,
                label: option.value,
                count: option.count
              }))}
              value={(pendingFilters[fieldName] as string[]) || []}
              onValueChange={(value) => {
                handleFilterChange(fieldName, value);
                // 批量筛选模式：不立即更新动态计数，等用户点击Apply Filters时再更新
              }}
              placeholder={`Select ${fieldConfig.displayName}`}
            />
          ) : fieldConfig.filterType ==="input" ? (
            <Input
              placeholder={`Enter ${fieldConfig.displayName.toLowerCase()}`}
              value={(pendingFilters[fieldName] as string) || ''}
              onChange={(e) => handleFilterChange(fieldName, e.target.value)}
              className="w-full text-xs h-8" onKeyPress={(e) => e.key ==="Enter" && handleSearch()}
            />
          ) : fieldConfig.filterType ==="date_range" ? (
            <DateRangePicker
              startDate={pendingFilters[`${fieldName}From`] as string || ''}
              endDate={pendingFilters[`${fieldName}To`] as string || ''}
              onStartDateChange={(date) => handleFilterChange(`${fieldName}From`, date)}
              onEndDateChange={(date) => handleFilterChange(`${fieldName}To`, date)}
              placeholder={`Select ${fieldConfig.displayName.toLowerCase()} range`}
            />
          ) : fieldConfig.filterType ==="checkbox" ? (
            <MultiSelect
              options={mergedOptions.map(option => ({
                value: option.value,
                label: option.value ==="true" ? 'Yes' : option.value ==="false" ? 'No' : option.value,
                count: option.count
              }))}
              value={(pendingFilters[fieldName] as string[]) || []}
              onValueChange={(value) => {
                handleFilterChange(fieldName, value);
                // 批量筛选模式：不立即更新动态计数，等用户点击Apply Filters时再更新
              }}
              placeholder={`Select ${fieldConfig.displayName}`}
            />
          ) : null}
        </div>
      );
    }).filter(Boolean);
  };

  // 权限检查完成且没有权限，显示权限限制提示
  if (permissionCheckComplete && !hasAccess) {
    return <AccessRestrictedAlert databaseCode={database} requiredLevel={requiredLevel as any} />;
  }

  // 如果权限检查未完成且用户状态已加载，显示简短的加载状态
  if (!permissionCheckComplete && user !== undefined) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600 mx-auto mb-2"></div>
          <p className="text-gray-600 text-sm">Verifying permissions...</p>
        </div>
      </div>
    );
  }



  return (
    <div className="flex">
      {/* Filter Panel - 固定位置的筛选面板 */}
      <div className={`${filterOpen ? 'w-72' : 'w-16'} transition-all duration-300 bg-gray-50 border-r border-gray-300 flex flex-col fixed top-16 left-0 bottom-0 z-50 overflow-hidden hidden md:block`}>
        {/* 固定头部 */}
        <div className="p-4 border-b border-gray-200 bg-white shadow-sm">
          <div className="flex items-center justify-between">
            <h3 className={`font-medium text-gray-800 ${!filterOpen ? 'hidden' : ''}`}>Filters</h3>
            <Button variant="ghost" size="sm" onClick={() => setFilterOpen(!filterOpen)} className="hover:bg-gray-100">
              {filterOpen ? <X className="h-4 w-4 text-gray-600" /> : <Filter className="h-4 w-4 text-gray-600" />}
            </Button>
          </div>
        </div>

        {filterOpen && (
          <div className="flex-1 flex flex-col min-h-0">
            <div className="flex-1 overflow-y-auto p-3 space-y-3 custom-scrollbar" style={{ maxHeight: 'calc(100vh - 200px)' }}>
              {/* Dynamic filters based on config */}
              {renderDynamicFilters()}
            </div>

            {/* Fixed bottom action buttons */}
            <div className="border-t border-gray-200 p-4 bg-white flex-shrink-0 relative z-60 shadow-lg">
              <div className="space-y-3">
                <div className="flex gap-2">
                  <Button onClick={handleSearch} disabled={loading} className="flex-1">
                    {loading ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : null}
                    Apply Filters
                  </Button>
                  <Button variant="ghost" onClick={resetFilters} disabled={loading}>
                    Clear
                  </Button>
                </div>


              </div>
            </div>
          </div>
        )}
      </div>

              {/* 数据库标题和功能按钮区域 */}
        <div ref={databaseTitleRef} className={`fixed top-16 ${filterOpen ? 'left-72' : 'left-16'} right-0 z-40 bg-white border-b border-gray-200 transition-all duration-300`}>
          <div className="px-6 py-3">
            <div className="flex justify-between items-center">
              <h1 className="text-2xl font-bold">{databaseName}</h1>
              <div className="flex gap-2">
                {/* 高级搜索组件 */}
                {config && (
                  <AdvancedSearch
                    onSearch={handleAdvancedSearch}
                    onClear={handleAdvancedSearchClear}
                    availableFields={config.fields.filter(f => f.isAdvancedSearchable)}
                    currentConditions={advancedSearchConditions}
                    metadata={metadata}
                  />
                )}
                <Button
                  variant="outline" size="sm" onClick={() => setShowStats(!showStats)}
                  className={`h-8 text-xs ${showStats ? "bg-blue-50 border-blue-200" : ""}`}
                >
                  <BarChart3 className="mr-1 h-3 w-3" />
                  {showStats ? 'Hide Statistics' : 'Show Statistics'}
                </Button>
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="outline" size="sm" className="h-8 text-xs">
                      <Download className="mr-1 h-3 w-3" /> Export
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent>
                    <DropdownMenuItem onClick={() => handleExport('csv')}>
                      Export as CSV
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => handleExport('excel')}>
                      Export as Excel
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
            </div>

            {/* 可折叠统计面板 - 使用绝对定位避免影响父容器高度 */}
            <div className="relative" ref={statsPanelRef}>
              <CollapsibleStatsPanel
                database={database}
                isOpen={showStats}
                onToggle={() => setShowStats(!showStats)}
                className="absolute top-0 left-0 right-0 z-50" maxHeight={statsPanelMaxHeight}
                filters={appliedFilters}
              />
            </div>
          </div>
        </div>

        {/* 搜索标签区域 */}
        {searchChips.hasActiveFilters && (
          <div className={`fixed ${filterOpen ? 'left-72' : 'left-16'} right-0 z-35 bg-white border-b border-gray-200 transition-all duration-300`} style={{
            top: searchChipsTop
          }}>
            <SearchChips
              chips={searchChips.chips}
              onRemoveChip={searchChips.handleRemoveChip}
              onClearAll={searchChips.handleClearAll}
              showClearAll={true}
              maxDisplay={10}
              compact={true}
            />
          </div>
        )}

        {/* 表头区域 - 支持第一列固定 */}
        <div ref={headerFixedRef} className={`fixed ${filterOpen ? 'left-72' : 'left-16'} right-0 z-25 bg-gray-50 border-b border-gray-200 transition-all duration-300 ${resizableColumns.isResizing ? 'resizing' : ''}`} style={{
          top: headerTop
        }}>
          {/* 列宽重置按钮 */}
          <div className="absolute right-2 top-1/2 transform -translate-y-1/2 z-40">
            <Button
              variant="ghost" size="sm" onClick={resizableColumns.resetColumnWidths}
              className="h-6 w-6 p-0 hover:bg-gray-200" title="Reset column widths">
              <RotateCcw className="h-3 w-3" />
            </Button>
          </div>
          <div
            ref={headerScrollRef}
            className="overflow-x-auto overflow-y-hidden" style={{
              scrollbarWidth: 'none',
              msOverflowStyle: 'none'
            }}
          >
            <div className="min-w-max">
              <div className="px-4 py-1.5">
                <div className="flex text-sm font-medium text-gray-700">
                  {tableHeaders.map((header, _index) => {
                    const isSortable = header.isSortable;
                    const isCurrentSort = sortBy === header.fieldName;
                    const isFirst = isFirstColumn(_index);
                    const isLast = _index === tableHeaders.length - 1;
                    const columnStyle = resizableColumns.getColumnStyle(header.fieldName);

                    return (
                      <div
                        key={header.fieldName}
                        className={`relative truncate ${
                          isSortable ? 'cursor-pointer hover:text-gray-900 select-none' : ''
                        } flex items-center gap-1 ${
                          isFirst ? 'sticky left-0 bg-gray-50 z-20 border-r border-gray-300' : ''
                        }`}
                        style={{
                          ...columnStyle,
                          ...(isFirst ? { left: 0 } : {})
                        }}
                        onClick={() => isSortable && handleSort(header.fieldName)}
                      >
                        <span className="truncate pr-2">{header.displayName}</span>
                        {isSortable && (
                          <div className="flex flex-col">
                            {isCurrentSort ? (
                              sortOrder ==="asc" ? (
                                <ChevronUp className="h-3 w-3 text-blue-600" />
                              ) : (
                                <ChevronDown className="h-3 w-3 text-blue-600" />
                              )
                            ) : (
                              <ArrowUpDown className="h-3 w-3 text-gray-400" />
                            )}
                          </div>
                        )}

                        {/* 列宽调整器 - 不在最后一列显示 */}
                        {!isLast && (
                          <ColumnResizer
                            fieldName={header.fieldName}
                            isResizing={resizableColumns.resizingColumn === header.fieldName}
                            isActive={resizableColumns.resizingColumn === header.fieldName}
                            onMouseDown={resizableColumns.handleMouseDown}
                          />
                        )}
                      </div>
                    );
                  })}
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* 数据表格区域 - 支持第一列固定 */}
        <div className={`absolute z-10 ${filterOpen ? 'left-72' : 'left-16'} right-0 bottom-0 transition-all duration-300`} style={{
          top: tableMarginTop,
          paddingTop: 0
        }}>
          <div
            ref={bodyScrollRef}
            className="overflow-auto custom-scrollbar border-l border-r border-b border-gray-200" style={{
              borderTop: 'none',
              maxHeight: 'calc(100vh - 240px)',
              minHeight: '200px'
            }}
          >
            <div className="min-w-max">
              <div className="bg-white" style={{ paddingTop: 0 }}>
            {loading ? (
              <div className="flex justify-center items-center py-12">
                <Loader2 className="h-8 w-8 animate-spin text-blue-600" />
                <span className="ml-2 text-gray-600">Loading...</span>
              </div>
            ) : error ? (
              <div className="flex justify-center items-center py-12">
                <div className="text-center">
                  <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
                  <p className="text-red-600 mb-2">Loading Failed</p>
                  <p className="text-gray-500 text-sm">{error}</p>
                  <Button
                    variant="outline" onClick={() => loadData(pagination.page, appliedFilters)}
                    className="mt-4">
                    Retry
                  </Button>
                </div>
              </div>
            ) : data.length === 0 ? (
              <div className="flex justify-center items-center py-12">
                <div className="text-center">
                  <Database className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-500">No data available</p>
                  {process.env.NODE_ENV === 'development' && (
                    <div className="mt-4 p-4 bg-gray-100 rounded text-left text-xs">
                      <p><strong>Debug Info:</strong></p>
                      <p>Applied Filters: {JSON.stringify(appliedFilters)}</p>
                      <p>Data Length: {data.length}</p>
                      <p>Loading: {loading.toString()}</p>
                      <p>Error: {error || 'none'}</p>
                      <p>Filters Initialized: {filtersInitialized.toString()}</p>
                    </div>
                  )}
                </div>
              </div>
            ) : (
              data.map((row, index) => (
                <div
                  key={row.id ? String(row.id) : `row-${index}`}
                  className="px-4 py-3 border-b border-gray-100 hover:bg-gray-50 cursor-pointer transition-colors" onClick={() => handleRowClick(row)}
                >
                  <div className="flex text-sm min-w-max">
                    {tableHeaders.map((header, headerIndex) => {
                      const isFirst = isFirstColumn(headerIndex);
                      const columnStyle = resizableColumns.getColumnStyle(header.fieldName);

                      const cellValue = smartFormatDate(
                        row[header.fieldName],
                        { fieldType: header.fieldType, fieldName: header.fieldName }
                      );

                      // 检查是否为长文本字段
                      const isLongTextField = ['structureOrUse', 'specifications', 'structure'].includes(header.fieldName);

                      // 截断长文本
                      const displayValue = isLongTextField && cellValue.length > 50
                        ? cellValue.substring(0, 50) + '...'
                        : cellValue;

                      return (
                        <div
                          key={header.fieldName}
                          className={`${
                            isFirst ? 'sticky left-0 bg-white z-10 border-r border-gray-300' : ''
                          }`}
                          style={{
                            ...columnStyle,
                            ...(isFirst ? { left: 0 } : {})
                          }}
                        >
                          <div className="px-2 py-1">
                            {header.todetail ? (
                              <Link
                                href={`/data/detail/${database}/${String(row.id)}`}
                                target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:text-blue-800 hover:underline cursor-pointer block line-clamp-3" title={cellValue}
                                onClick={(e) => {
                                  e.stopPropagation();
                                  handleRowClick(row);
                                }}
                              >
                                {displayValue}
                              </Link>
                            ) : (
                              <span
                                className="text-gray-900 cursor-help block line-clamp-3" title={cellValue}
                              >
                                {displayValue}
                              </span>
                            )}
                          </div>
                        </div>
                      );
                    })}
                  </div>
                </div>
              ))
            )}
            </div>
          </div>
        </div>

        {/* 固定底部分页区域 */}
        <div ref={footerRef} className={`fixed bottom-0 ${filterOpen ? 'left-72' : 'left-16'} right-0 z-30 p-4 bg-white border-t border-gray-200 transition-all duration-300`}>
          <div className="flex justify-between items-center">
            <span className="text-sm text-gray-600">
              Showing {(pagination.page - 1) * pagination.limit + 1} - {Math.min(pagination.page * pagination.limit, pagination.totalCount)} of {pagination.totalCount} records
            </span>
            <div className="flex items-center space-x-4">
              <Button
                variant="outline" size="sm" onClick={() => handlePageChange('prev')}
                disabled={pagination.page <= 1 || loading}
              >
                <ChevronLeft className="h-4 w-4" />
                Previous
              </Button>

              <div className="flex flex-col items-center">
                <span className="text-sm">
                  Page {pagination.page} of {Math.min(pagination.totalPages, pagination.maxPages)}
                  {pagination.totalPages > pagination.maxPages && (
                    <span className="text-gray-500"> (of {pagination.totalPages} total)</span>
                  )}
                </span>
                {pagination.totalPages > pagination.maxPages && (
                  <span className="text-xs text-amber-600 mt-1">
                    Showing first {pagination.maxPages} pages only
                  </span>
                )}
              </div>

              {/* 跳转到页功能 */}
              <div className="flex items-center space-x-2">
                <span className="text-sm text-gray-600">Go to:</span>
                <input
                  type="number" min="1" max={Math.min(pagination.totalPages || 1, pagination.maxPages || 100)}
                  value={jumpToPage}
                  onChange={(e) => setJumpToPage(e.target.value)}
                  onKeyPress={handleJumpInputKeyPress}
                  placeholder="Page" className="w-16 px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent" disabled={loading}
                />
                <Button
                  variant="outline" size="sm" onClick={handleJumpToPage}
                  disabled={loading || !jumpToPage}
                  className="px-3">
                  Go
                </Button>
              </div>

              <Button
                variant="outline" size="sm" onClick={() => handlePageChange('next')}
                disabled={!pagination.hasNext || loading}
              >
                Next
                <ChevronRight className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Mobile Filter Sheet */}
      <Sheet open={mobileFilterOpen} onOpenChange={setMobileFilterOpen}>
        <SheetTrigger asChild>
          <div className="md:hidden fixed bottom-4 right-4 z-50 flex flex-col gap-2">
            {showStats && (
              <Button
                onClick={() => setShowStats(false)}
                variant="outline" className="rounded-full h-12 w-12 shadow-lg bg-white">
                <BarChart3 className="h-5 w-5" />
              </Button>
            )}
            <Button className="rounded-full h-12 w-12 shadow-lg">
              <Filter className="h-5 w-5" />
            </Button>
          </div>
        </SheetTrigger>
        <SheetContent side="right" className="w-72 p-0">
          <SheetHeader className="p-4 border-b">
            <SheetTitle>Filters</SheetTitle>
          </SheetHeader>
          <div className="flex flex-col h-full">
            <div className="flex-1 overflow-y-auto p-3 space-y-3 custom-scrollbar">
              {/* Dynamic filters */}
              {renderDynamicFilters()}
            </div>

            {/* Fixed bottom action buttons */}
            <div className="border-t border-gray-100 p-4 bg-white">
              <div className="space-y-3">
                <div className="flex gap-2">
                  <Button onClick={handleSearch} disabled={loading} className="flex-1">
                    {loading ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : null}
                    Apply Filters
                  </Button>
                  <Button variant="ghost" onClick={resetFilters} disabled={loading}>
                    Clear
                  </Button>
                </div>


              </div>
            </div>
          </div>
        </SheetContent>
      </Sheet>
    </div>
  );
}
